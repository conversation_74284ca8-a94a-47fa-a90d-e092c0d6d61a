name: Lint Code Base

on:
  pull_request:
    branches: [main]

jobs:
  build:
    name: Lint Code Base
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Lint Code Base
        uses: super-linter/super-linter/slim@v8
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          LOG_LEVEL: WARN
          SHELLCHECK_OPTS: -e SC1091 -e 2086
          VALIDATE_ALL_CODEBASE: false
          FILTER_REGEX_EXCLUDE: "^(\\.github/|\\.vscode/).*|CODE_OF_CONDUCT.md|CHANGELOG.md|GOVERNANCE.md|\\.mkdocs/overrides/.*|docs/404.html"
          VALIDATE_PYTHON_BLACK: false
          VALIDATE_PYTHON_FLAKE8: false
          VALIDATE_PYTHON_ISORT: false
          VALIDATE_PYTHON_MYPY: false
          VALIDATE_PYTHON_PYLINT: false
          VALIDATE_CHECKOV: false
          VALIDATE_NATURAL_LANGUAGE: false
          MARKDOWN_CONFIG_FILE: ".markdownlint.json"
          VALIDATE_MARKDOWN_PRETTIER: false
          TYPESCRIPT_ES_CONFIG_FILE: ".eslintrc.js"
          VALIDATE_JAVASCRIPT_PRETTIER: false
          VALIDATE_JSON_PRETTIER: false
          VALIDATE_YAML_PRETTIER: false
          VALIDATE_GIT_COMMITLINT: false
