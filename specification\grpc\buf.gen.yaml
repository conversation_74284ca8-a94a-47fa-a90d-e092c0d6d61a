# buf generate
# Configuration for the buf generate command
# Uses remote plugins, no separate install required.
# This config generates python, go and java (protobuf) source.
version: v2
plugins:
  # Python
  - remote: buf.build/protocolbuffers/python:v29.3
    out: src/python
  - remote: buf.build/grpc/python
    out: src/python

  # Go
  - remote: buf.build/protocolbuffers/go
    out: src/go

  - remote: buf.build/grpc/go
    out: src/go

  # Java
  - remote: buf.build/protocolbuffers/java
    out: src/java

  - remote: buf.build/grpc/java
    out: src/java

  # TypeScript
  - remote: buf.build/community/stephenh-ts-proto:v1.165.0
    out: src/typescript
