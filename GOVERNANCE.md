# Agent2Agent (A2A) Governance

**The Agent to Agent project is governed by the Technical Steering Committee. The Committee has seven seats, each held by the following companies:**

1. <PERSON> - <PERSON>, Principal Engineer - [@ToddSegal](https://github.com/ToddSegal)
2. <PERSON> <PERSON> <PERSON><PERSON>, Partner API Architect - [@darrelmiller](https://github.com/darrelmiller)
3. Cisco <PERSON> <PERSON>, Principal Engineer - [@muscariello](https://github.com/muscariello)
4. Amazon Web Services - <PERSON>, Principal Engineer - [@000-000-000-000-000](https://github.com/000-000-000-000-000)
5. Salesforce - <PERSON>, Sr. Director Product Management - [@samuelsharaf](https://github.com/samuelsharaf)
6. <PERSON><PERSON><PERSON> - <PERSON>, AI Ecosystem Director - [@hughesthe1st](https://github.com/hughesthe1st)
7. SA<PERSON> - <PERSON><PERSON><PERSON>, Vice President - [LinkedIn](https://www.linkedin.com/in/siva-kumar-n/)

## Mission and Scope of the Project

1. The mission of the Project is to help AI agents across different ecosystems communicate with each other.  The Project includes collaborative development of the following components:
    1. the Agent2Agent Protocol (the "Protocol");
    2. a SDK for designing implementations of the Protocol and related software components; and
    3. documentation and other artifacts related to the Project.
2. The scope of the Project includes collaborative development under the Project License (as defined herein) supporting the mission, including documentation, testing, integration and the creation of other artifacts that aid the development, deployment, operation or adoption of the open source project.

## Technical Steering Committee

1. The Technical Steering Committee (the "TSC") will be responsible for all technical oversight of the open source Project.
2. TSC Composition
   a. "Startup Phase."  At the inception of the Project, each organization listed in the [`GOVERNANCE`](GOVERNANCE.md) file in the governance repository of the Project will have the right to appoint (and remove and replace) one employee to serve as a voting member of the TSC.
   b. "Steady State." The TSC will decide upon a "steady state" composition of the TSC (whether by election, sub-project technical leads, or other method as determined by the TSC) for composition of the TSC from the date that is 18 months following the inception of the Project, or at such other point as determined by the TSC.
The TSC may choose an alternative approach for determining the voting members of the TSC, and any such alternative approach will be documented in the GOVERNANCE file.  Any meetings of the Technical Steering Committee are intended to be open to the public, and can be conducted electronically, via teleconference, or in person.
3. TSC projects generally will involve Contributors and Maintainers. The TSC may adopt or modify roles so long as the roles are documented in the CONTRIBUTING file. Unless otherwise documented:
            a. Contributors include anyone in the technical community that contributes code, documentation, or other technical artifacts to the Project;
            b. Maintainers are Contributors who have earned the ability to modify ("commit") source code, documentation or other technical artifacts in a project's repository; and
            c. A Contributor may become a Maintainer by a vote of the TSC. A Maintainer may be removed by a vote of the TSC.
            d. Participation in the Project through becoming a Contributor and Maintainer is open to anyone so long as they abide by the terms of this Charter.
4. The TSC may (1) establish work flow procedures for the submission, approval, and closure/archiving of projects, (2) set requirements for the promotion of Contributors to Maintainer status, as applicable, and (3) amend, adjust, refine and/or eliminate the roles of Contributors, and Maintainer, and create new roles, and publicly document any TSC roles, as it sees fit.
5. The TSC may elect a TSC Chair, who will preside over meetings of the TSC and will serve until their resignation or replacement by the TSC.
6. Responsibilities: The TSC will be responsible for all aspects of oversight relating to the Project, which may include:
   1. coordinating the technical direction of the Project;
   2. approving project or system proposals (including, but not limited to, incubation, deprecation, and changes to a sub-project's scope);
   3. organizing sub-projects and removing sub-projects;
   4. creating sub-committees or working groups to focus on cross-project technical issues and requirements;
   5. appointing representatives to work with other open source or open standards communities;
   6. establishing community norms, workflows, issuing releases, and security issue reporting policies;
   7. approving and implementing policies and processes for contributing (to be published in the [`CONTRIBUTING`](CONTRIBUTING.md) file) and coordinating with the series manager of the Project (as provided for in the Series Agreement, the "Series Manager") to resolve matters or concerns that may arise as set forth in Section 7 of this Charter;
   8. discussions, seeking consensus, and where necessary, voting on technical matters relating to the code base that affect multiple projects; and
   9. coordinating any marketing, events, or communications regarding the Project.

## TSC Voting

While the Project aims to operate as a consensus-based community, if any TSC decision requires a vote to move the Project forward, the voting members of the TSC will vote on a one vote per voting member basis.
Quorum for TSC meetings requires at least fifty percent of all voting members of the TSC to be present. The TSC may continue to meet if quorum is not met but will be prevented from making any decisions at the meeting. Except as provided in Section 7.c. and 8.a, decisions by vote at a meeting require a majority vote of those in attendance, provided quorum is met. Decisions made by electronic vote without a meeting require a majority vote of all voting members of the TSC.

## Project Communications

The A2A project utilizes Discord for chat conversations about the project. All are welcome and encouraged to join the [A2A Discord](http://discord.gg/a2aprotocol). Discussion is encouraged however we do remind the community that chat is ephemeral, and not all members of the project are active in chat at the same time. Therefore, any discussions about feature proposals, significant changes to the project architecture or governance, etc. should be held in GitHub with adequate notice and time for comment. Look for specifics on that timing coming soon as the TSC ramps up. Just keep in mind - our goal is that GitHub is the source of truth for significant project decisions.
Additional communication avenues will likely be added - stay tuned.

## TSC Meetings

Our hope is that the first TSC meeting will be held in August 2025. Once the TSC representatives are finalized, scheduling will begin. In the interim we have drafted a [working doc for TSC Meeting Agendas](https://docs.google.com/document/d/1Dx6qYfCjSChHKRMwLJcvtDjq6igYTAKFW9Vg1IMPCUk/view).
