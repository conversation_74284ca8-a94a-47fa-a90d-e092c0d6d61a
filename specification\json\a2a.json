{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"A2AError": {"anyOf": [{"$ref": "#/definitions/JSONParseError"}, {"$ref": "#/definitions/InvalidRequestError"}, {"$ref": "#/definitions/MethodNotFoundError"}, {"$ref": "#/definitions/InvalidParamsError"}, {"$ref": "#/definitions/InternalError"}, {"$ref": "#/definitions/TaskNotFoundError"}, {"$ref": "#/definitions/TaskNotCancelableError"}, {"$ref": "#/definitions/PushNotificationNotSupportedError"}, {"$ref": "#/definitions/UnsupportedOperationError"}, {"$ref": "#/definitions/ContentTypeNotSupportedError"}, {"$ref": "#/definitions/InvalidAgentResponseError"}, {"$ref": "#/definitions/AuthenticatedExtendedCardNotConfiguredError"}], "description": "A discriminated union of all standard JSON-RPC and A2A-specific error types."}, "A2ARequest": {"anyOf": [{"$ref": "#/definitions/SendMessageRequest"}, {"$ref": "#/definitions/SendStreamingMessageRequest"}, {"$ref": "#/definitions/GetTaskRequest"}, {"$ref": "#/definitions/CancelTaskRequest"}, {"$ref": "#/definitions/SetTaskPushNotificationConfigRequest"}, {"$ref": "#/definitions/GetTaskPushNotificationConfigRequest"}, {"$ref": "#/definitions/TaskResubscriptionRequest"}, {"$ref": "#/definitions/ListTaskPushNotificationConfigRequest"}, {"$ref": "#/definitions/DeleteTaskPushNotificationConfigRequest"}, {"$ref": "#/definitions/GetAuthenticatedExtendedCardRequest"}], "description": "A discriminated union representing all possible JSON-RPC 2.0 requests supported by the A2A specification."}, "APIKeySecurityScheme": {"description": "Defines a security scheme using an API key.", "properties": {"description": {"description": "An optional description for the security scheme.", "type": "string"}, "in": {"description": "The location of the API key.", "enum": ["cookie", "header", "query"], "type": "string"}, "name": {"description": "The name of the header, query, or cookie parameter to be used.", "type": "string"}, "type": {"const": "<PERSON><PERSON><PERSON><PERSON>", "description": "The type of the security scheme. Must be 'api<PERSON>ey'.", "type": "string"}}, "required": ["in", "name", "type"], "type": "object"}, "AgentCapabilities": {"description": "Defines optional capabilities supported by an agent.", "properties": {"extensions": {"description": "A list of protocol extensions supported by the agent.", "items": {"$ref": "#/definitions/AgentExtension"}, "type": "array"}, "pushNotifications": {"description": "Indicates if the agent supports sending push notifications for asynchronous task updates.", "type": "boolean"}, "stateTransitionHistory": {"description": "Indicates if the agent provides a history of state transitions for a task.", "type": "boolean"}, "streaming": {"description": "Indicates if the agent supports Server-Sent Events (SSE) for streaming responses.", "type": "boolean"}}, "type": "object"}, "AgentCard": {"description": "The AgentCard is a self-describing manifest for an agent. It provides essential\nmetadata including the agent's identity, capabilities, skills, supported\ncommunication methods, and security requirements.", "properties": {"additionalInterfaces": {"description": "A list of additional supported interfaces (transport and URL combinations).\nThis allows agents to expose multiple transports, potentially at different URLs.\n\nBest practices:\n- SHOULD include all supported transports for completeness\n- SHOULD include an entry matching the main 'url' and 'preferredTransport'\n- MAY reuse URLs if multiple transports are available at the same endpoint\n- MUST accurately declare the transport available at each URL\n\nClients can select any interface from this list based on their transport capabilities\nand preferences. This enables transport negotiation and fallback scenarios.", "items": {"$ref": "#/definitions/AgentInterface"}, "type": "array"}, "capabilities": {"$ref": "#/definitions/AgentCapabilities", "description": "A declaration of optional capabilities supported by the agent."}, "defaultInputModes": {"description": "Default set of supported input MIME types for all skills, which can be\noverridden on a per-skill basis.", "items": {"type": "string"}, "type": "array"}, "defaultOutputModes": {"description": "Default set of supported output MIME types for all skills, which can be\noverridden on a per-skill basis.", "items": {"type": "string"}, "type": "array"}, "description": {"description": "A human-readable description of the agent, assisting users and other agents\nin understanding its purpose.", "examples": ["Agent that helps users with recipes and cooking."], "type": "string"}, "documentationUrl": {"description": "An optional URL to the agent's documentation.", "type": "string"}, "iconUrl": {"description": "An optional URL to an icon for the agent.", "type": "string"}, "name": {"description": "A human-readable name for the agent.", "examples": ["Recipe Agent"], "type": "string"}, "preferredTransport": {"default": "JSONRPC", "description": "The transport protocol for the preferred endpoint (the main 'url' field).\nIf not specified, defaults to 'JSONRPC'.\n\nIMPORTANT: The transport specified here MUST be available at the main 'url'.\nThis creates a binding between the main URL and its supported transport protocol.\nClients should prefer this transport and URL combination when both are supported.", "examples": ["JSONRPC", "GRPC", "HTTP+JSON"], "type": "string"}, "protocolVersion": {"default": "0.3.0", "description": "The version of the A2A protocol this agent supports.", "type": "string"}, "provider": {"$ref": "#/definitions/AgentProvider", "description": "Information about the agent's service provider."}, "security": {"description": "A list of security requirement objects that apply to all agent interactions. Each object\nlists security schemes that can be used. Follows the OpenAPI 3.0 Security Requirement Object.\nThis list can be seen as an OR of ANDs. Each object in the list describes one possible\nset of security requirements that must be present on a request. This allows specifying,\nfor example, \"callers must either use OAuth OR an API Key AND mTLS.\"", "examples": [[{"oauth": ["read"]}, {"api-key": [], "mtls": []}]], "items": {"additionalProperties": {"items": {"type": "string"}, "type": "array"}, "type": "object"}, "type": "array"}, "securitySchemes": {"additionalProperties": {"$ref": "#/definitions/SecurityScheme"}, "description": "A declaration of the security schemes available to authorize requests. The key is the\nscheme name. Follows the OpenAPI 3.0 Security Scheme Object.", "type": "object"}, "signatures": {"description": "JSON Web Signatures computed for this AgentCard.", "items": {"$ref": "#/definitions/AgentCardSignature"}, "type": "array"}, "skills": {"description": "The set of skills, or distinct capabilities, that the agent can perform.", "items": {"$ref": "#/definitions/AgentSkill"}, "type": "array"}, "supportsAuthenticatedExtendedCard": {"description": "If true, the agent can provide an extended agent card with additional details\nto authenticated users. Defaults to false.", "type": "boolean"}, "url": {"description": "The preferred endpoint URL for interacting with the agent.\nThis URL MUST support the transport specified by 'preferredTransport'.", "examples": ["https://api.example.com/a2a/v1"], "type": "string"}, "version": {"description": "The agent's own version number. The format is defined by the provider.", "examples": ["1.0.0"], "type": "string"}}, "required": ["capabilities", "defaultInputModes", "defaultOutputModes", "description", "name", "protocolVersion", "skills", "url", "version"], "type": "object"}, "AgentCardSignature": {"description": "AgentCardSignature represents a JWS signature of an AgentCard.\nThis follows the JSON format of an RFC 7515 JSON Web Signature (JWS).", "properties": {"header": {"additionalProperties": {}, "description": "The unprotected JWS header values.", "type": "object"}, "protected": {"description": "The protected JWS header for the signature. This is a Base64url-encoded\nJSON object, as per RFC 7515.", "type": "string"}, "signature": {"description": "The computed signature, Base64url-encoded.", "type": "string"}}, "required": ["protected", "signature"], "type": "object"}, "AgentExtension": {"description": "A declaration of a protocol extension supported by an Agent.", "examples": [{"description": "Google OAuth 2.0 authentication", "required": false, "uri": "https://developers.google.com/identity/protocols/oauth2"}], "properties": {"description": {"description": "A human-readable description of how this agent uses the extension.", "type": "string"}, "params": {"additionalProperties": {}, "description": "Optional, extension-specific configuration parameters.", "type": "object"}, "required": {"description": "If true, the client must understand and comply with the extension's requirements\nto interact with the agent.", "type": "boolean"}, "uri": {"description": "The unique URI identifying the extension.", "type": "string"}}, "required": ["uri"], "type": "object"}, "AgentInterface": {"description": "Declares a combination of a target URL and a transport protocol for interacting with the agent.\nThis allows agents to expose the same functionality over multiple transport mechanisms.", "properties": {"transport": {"description": "The transport protocol supported at this URL.", "examples": ["JSONRPC", "GRPC", "HTTP+JSON"], "type": "string"}, "url": {"description": "The URL where this interface is available. Must be a valid absolute HTTPS URL in production.", "examples": ["https://api.example.com/a2a/v1", "https://grpc.example.com/a2a", "https://rest.example.com/v1"], "type": "string"}}, "required": ["transport", "url"], "type": "object"}, "AgentProvider": {"description": "Represents the service provider of an agent.", "examples": [{"organization": "Google", "url": "https://ai.google.dev"}], "properties": {"organization": {"description": "The name of the agent provider's organization.", "type": "string"}, "url": {"description": "A URL for the agent provider's website or relevant documentation.", "type": "string"}}, "required": ["organization", "url"], "type": "object"}, "AgentSkill": {"description": "Represents a distinct capability or function that an agent can perform.", "properties": {"description": {"description": "A detailed description of the skill, intended to help clients or users\nunderstand its purpose and functionality.", "type": "string"}, "examples": {"description": "Example prompts or scenarios that this skill can handle. Provides a hint to\nthe client on how to use the skill.", "examples": [["I need a recipe for bread"]], "items": {"type": "string"}, "type": "array"}, "id": {"description": "A unique identifier for the agent's skill.", "type": "string"}, "inputModes": {"description": "The set of supported input MIME types for this skill, overriding the agent's defaults.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "A human-readable name for the skill.", "type": "string"}, "outputModes": {"description": "The set of supported output MIME types for this skill, overriding the agent's defaults.", "items": {"type": "string"}, "type": "array"}, "security": {"description": "Security schemes necessary for the agent to leverage this skill.\nAs in the overall AgentCard.security, this list represents a logical OR of security\nrequirement objects. Each object is a set of security schemes that must be used together\n(a logical AND).", "examples": [[{"google": ["oidc"]}]], "items": {"additionalProperties": {"items": {"type": "string"}, "type": "array"}, "type": "object"}, "type": "array"}, "tags": {"description": "A set of keywords describing the skill's capabilities.", "examples": [["cooking", "customer support", "billing"]], "items": {"type": "string"}, "type": "array"}}, "required": ["description", "id", "name", "tags"], "type": "object"}, "Artifact": {"description": "Represents a file, data structure, or other resource generated by an agent during a task.", "properties": {"artifactId": {"description": "A unique identifier (e.g. UUID) for the artifact within the scope of the task.", "type": "string"}, "description": {"description": "An optional, human-readable description of the artifact.", "type": "string"}, "extensions": {"description": "The URIs of extensions that are relevant to this artifact.", "items": {"type": "string"}, "type": "array"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions. The key is an extension-specific identifier.", "type": "object"}, "name": {"description": "An optional, human-readable name for the artifact.", "type": "string"}, "parts": {"description": "An array of content parts that make up the artifact.", "items": {"$ref": "#/definitions/Part"}, "type": "array"}}, "required": ["artifactId", "parts"], "type": "object"}, "AuthenticatedExtendedCardNotConfiguredError": {"description": "An A2A-specific error indicating that the agent does not have an Authenticated Extended Card configured", "properties": {"code": {"const": -32007, "description": "The error code for when an authenticated extended card is not configured.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Authenticated Extended Card is not configured", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "AuthorizationCodeOAuthFlow": {"description": "Defines configuration details for the OAuth 2.0 Authorization Code flow.", "properties": {"authorizationUrl": {"description": "The authorization URL to be used for this flow.\nThis MUST be a URL and use TLS.", "type": "string"}, "refreshUrl": {"description": "The URL to be used for obtaining refresh tokens.\nThis MUST be a URL and use TLS.", "type": "string"}, "scopes": {"additionalProperties": {"type": "string"}, "description": "The available scopes for the OAuth2 security scheme. A map between the scope\nname and a short description for it.", "type": "object"}, "tokenUrl": {"description": "The token URL to be used for this flow.\nThis MUST be a URL and use TLS.", "type": "string"}}, "required": ["authorizationUrl", "scopes", "tokenUrl"], "type": "object"}, "CancelTaskRequest": {"description": "Represents a JSON-RPC request for the `tasks/cancel` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/cancel", "description": "The method name. Must be 'tasks/cancel'.", "type": "string"}, "params": {"$ref": "#/definitions/TaskIdParams", "description": "The parameters identifying the task to cancel."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "CancelTaskResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/CancelTaskSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/cancel` method."}, "CancelTaskSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/cancel` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"$ref": "#/definitions/Task", "description": "The result, containing the final state of the canceled Task object."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "ClientCredentialsOAuthFlow": {"description": "Defines configuration details for the OAuth 2.0 Client Credentials flow.", "properties": {"refreshUrl": {"description": "The URL to be used for obtaining refresh tokens. This MUST be a URL.", "type": "string"}, "scopes": {"additionalProperties": {"type": "string"}, "description": "The available scopes for the OAuth2 security scheme. A map between the scope\nname and a short description for it.", "type": "object"}, "tokenUrl": {"description": "The token URL to be used for this flow. This MUST be a URL.", "type": "string"}}, "required": ["scopes", "tokenUrl"], "type": "object"}, "ContentTypeNotSupportedError": {"description": "An A2A-specific error indicating an incompatibility between the requested\ncontent types and the agent's capabilities.", "properties": {"code": {"const": -32005, "description": "The error code for an unsupported content type.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Incompatible content types", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "DataPart": {"description": "Represents a structured data segment (e.g., JSON) within a message or artifact.", "properties": {"data": {"additionalProperties": {}, "description": "The structured data content.", "type": "object"}, "kind": {"const": "data", "description": "The type of this part, used as a discriminator. Always 'data'.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with this part.", "type": "object"}}, "required": ["data", "kind"], "type": "object"}, "DeleteTaskPushNotificationConfigParams": {"description": "Defines parameters for deleting a specific push notification configuration for a task.", "properties": {"id": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with the request.", "type": "object"}, "pushNotificationConfigId": {"description": "The ID of the push notification configuration to delete.", "type": "string"}}, "required": ["id", "pushNotificationConfigId"], "type": "object"}, "DeleteTaskPushNotificationConfigRequest": {"description": "Represents a JSON-RPC request for the `tasks/pushNotificationConfig/delete` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/pushNotificationConfig/delete", "description": "The method name. Must be 'tasks/pushNotificationConfig/delete'.", "type": "string"}, "params": {"$ref": "#/definitions/DeleteTaskPushNotificationConfigParams", "description": "The parameters identifying the push notification configuration to delete."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "DeleteTaskPushNotificationConfigResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/DeleteTaskPushNotificationConfigSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/pushNotificationConfig/delete` method."}, "DeleteTaskPushNotificationConfigSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/delete` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"description": "The result is null on successful deletion.", "type": "null"}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "FileBase": {"description": "Defines base properties for a file.", "properties": {"mimeType": {"description": "The MIME type of the file (e.g., \"application/pdf\").", "type": "string"}, "name": {"description": "An optional name for the file (e.g., \"document.pdf\").", "type": "string"}}, "type": "object"}, "FilePart": {"description": "Represents a file segment within a message or artifact. The file content can be\nprovided either directly as bytes or as a URI.", "properties": {"file": {"anyOf": [{"$ref": "#/definitions/FileWithBytes"}, {"$ref": "#/definitions/FileWithUri"}], "description": "The file content, represented as either a URI or as base64-encoded bytes."}, "kind": {"const": "file", "description": "The type of this part, used as a discriminator. Always 'file'.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with this part.", "type": "object"}}, "required": ["file", "kind"], "type": "object"}, "FileWithBytes": {"description": "Represents a file with its content provided directly as a base64-encoded string.", "properties": {"bytes": {"description": "The base64-encoded content of the file.", "type": "string"}, "mimeType": {"description": "The MIME type of the file (e.g., \"application/pdf\").", "type": "string"}, "name": {"description": "An optional name for the file (e.g., \"document.pdf\").", "type": "string"}}, "required": ["bytes"], "type": "object"}, "FileWithUri": {"description": "Represents a file with its content located at a specific URI.", "properties": {"mimeType": {"description": "The MIME type of the file (e.g., \"application/pdf\").", "type": "string"}, "name": {"description": "An optional name for the file (e.g., \"document.pdf\").", "type": "string"}, "uri": {"description": "A URL pointing to the file's content.", "type": "string"}}, "required": ["uri"], "type": "object"}, "GetAuthenticatedExtendedCardRequest": {"description": "Represents a JSON-RPC request for the `agent/getAuthenticatedExtendedCard` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "agent/getAuthenticatedExtendedCard", "description": "The method name. Must be 'agent/getAuthenticatedExtendedCard'.", "type": "string"}}, "required": ["id", "jsonrpc", "method"], "type": "object"}, "GetAuthenticatedExtendedCardResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/GetAuthenticatedExtendedCardSuccessResponse"}], "description": "Represents a JSON-RPC response for the `agent/getAuthenticatedExtendedCard` method."}, "GetAuthenticatedExtendedCardSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `agent/getAuthenticatedExtendedCard` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"$ref": "#/definitions/AgentCard", "description": "The result is an Agent Card object."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "GetTaskPushNotificationConfigParams": {"description": "Defines parameters for fetching a specific push notification configuration for a task.", "properties": {"id": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with the request.", "type": "object"}, "pushNotificationConfigId": {"description": "The ID of the push notification configuration to retrieve.", "type": "string"}}, "required": ["id"], "type": "object"}, "GetTaskPushNotificationConfigRequest": {"description": "Represents a JSON-RPC request for the `tasks/pushNotificationConfig/get` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/pushNotificationConfig/get", "description": "The method name. Must be 'tasks/pushNotificationConfig/get'.", "type": "string"}, "params": {"anyOf": [{"$ref": "#/definitions/TaskIdParams"}, {"$ref": "#/definitions/GetTaskPushNotificationConfigParams"}], "description": "The parameters for getting a push notification configuration."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "GetTaskPushNotificationConfigResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/GetTaskPushNotificationConfigSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/pushNotificationConfig/get` method."}, "GetTaskPushNotificationConfigSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/get` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"$ref": "#/definitions/TaskPushNotificationConfig", "description": "The result, containing the requested push notification configuration."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "GetTaskRequest": {"description": "Represents a JSON-RPC request for the `tasks/get` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/get", "description": "The method name. Must be 'tasks/get'.", "type": "string"}, "params": {"$ref": "#/definitions/TaskQueryParams", "description": "The parameters for querying a task."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "GetTaskResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/GetTaskSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/get` method."}, "GetTaskSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/get` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"$ref": "#/definitions/Task", "description": "The result, containing the requested Task object."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "HTTPAuthSecurityScheme": {"description": "Defines a security scheme using HTTP authentication.", "properties": {"bearerFormat": {"description": "A hint to the client to identify how the bearer token is formatted (e.g., \"JWT\").\nThis is primarily for documentation purposes.", "type": "string"}, "description": {"description": "An optional description for the security scheme.", "type": "string"}, "scheme": {"description": "The name of the HTTP Authentication scheme to be used in the Authorization header,\nas defined in RFC7235 (e.g., \"Bearer\").\nThis value should be registered in the IANA Authentication Scheme registry.", "type": "string"}, "type": {"const": "http", "description": "The type of the security scheme. Must be 'http'.", "type": "string"}}, "required": ["scheme", "type"], "type": "object"}, "ImplicitOAuthFlow": {"description": "Defines configuration details for the OAuth 2.0 Implicit flow.", "properties": {"authorizationUrl": {"description": "The authorization URL to be used for this flow. This MUST be a URL.", "type": "string"}, "refreshUrl": {"description": "The URL to be used for obtaining refresh tokens. This MUST be a URL.", "type": "string"}, "scopes": {"additionalProperties": {"type": "string"}, "description": "The available scopes for the OAuth2 security scheme. A map between the scope\nname and a short description for it.", "type": "object"}}, "required": ["authorizationUrl", "scopes"], "type": "object"}, "InternalError": {"description": "An error indicating an internal error on the server.", "properties": {"code": {"const": -32603, "description": "The error code for an internal server error.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Internal error", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "InvalidAgentResponseError": {"description": "An A2A-specific error indicating that the agent returned a response that\ndoes not conform to the specification for the current method.", "properties": {"code": {"const": -32006, "description": "The error code for an invalid agent response.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Invalid agent response", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "InvalidParamsError": {"description": "An error indicating that the method parameters are invalid.", "properties": {"code": {"const": -32602, "description": "The error code for an invalid parameters error.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Invalid parameters", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "InvalidRequestError": {"description": "An error indicating that the JSON sent is not a valid Request object.", "properties": {"code": {"const": -32600, "description": "The error code for an invalid request.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Request payload validation error", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "JSONParseError": {"description": "An error indicating that the server received invalid JSON.", "properties": {"code": {"const": -32700, "description": "The error code for a JSON parse error.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Invalid JSON payload", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "JSONRPCError": {"description": "Represents a JSON-RPC 2.0 Error object, included in an error response.", "properties": {"code": {"description": "A number that indicates the error type that occurred.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"description": "A string providing a short description of the error.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "JSONRPCErrorResponse": {"description": "Represents a JSON-RPC 2.0 Error Response object.", "properties": {"error": {"anyOf": [{"$ref": "#/definitions/JSONRPCError"}, {"$ref": "#/definitions/JSONParseError"}, {"$ref": "#/definitions/InvalidRequestError"}, {"$ref": "#/definitions/MethodNotFoundError"}, {"$ref": "#/definitions/InvalidParamsError"}, {"$ref": "#/definitions/InternalError"}, {"$ref": "#/definitions/TaskNotFoundError"}, {"$ref": "#/definitions/TaskNotCancelableError"}, {"$ref": "#/definitions/PushNotificationNotSupportedError"}, {"$ref": "#/definitions/UnsupportedOperationError"}, {"$ref": "#/definitions/ContentTypeNotSupportedError"}, {"$ref": "#/definitions/InvalidAgentResponseError"}, {"$ref": "#/definitions/AuthenticatedExtendedCardNotConfiguredError"}], "description": "An object describing the error that occurred."}, "id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}}, "required": ["error", "id", "jsonrpc"], "type": "object"}, "JSONRPCMessage": {"description": "Defines the base structure for any JSON-RPC 2.0 request, response, or notification.", "properties": {"id": {"description": "A unique identifier established by the client. It must be a String, a Number, or null.\nThe server must reply with the same value in the response. This property is omitted for notifications.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}}, "required": ["jsonrpc"], "type": "object"}, "JSONRPCRequest": {"description": "Represents a JSON-RPC 2.0 Request object.", "properties": {"id": {"description": "A unique identifier established by the client. It must be a String, a Number, or null.\nThe server must reply with the same value in the response. This property is omitted for notifications.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"description": "A string containing the name of the method to be invoked.", "type": "string"}, "params": {"additionalProperties": {}, "description": "A structured value holding the parameter values to be used during the method invocation.", "type": "object"}}, "required": ["jsonrpc", "method"], "type": "object"}, "JSONRPCResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/SendMessageSuccessResponse"}, {"$ref": "#/definitions/SendStreamingMessageSuccessResponse"}, {"$ref": "#/definitions/GetTaskSuccessResponse"}, {"$ref": "#/definitions/CancelTaskSuccessResponse"}, {"$ref": "#/definitions/SetTaskPushNotificationConfigSuccessResponse"}, {"$ref": "#/definitions/GetTaskPushNotificationConfigSuccessResponse"}, {"$ref": "#/definitions/ListTaskPushNotificationConfigSuccessResponse"}, {"$ref": "#/definitions/DeleteTaskPushNotificationConfigSuccessResponse"}, {"$ref": "#/definitions/GetAuthenticatedExtendedCardSuccessResponse"}], "description": "A discriminated union representing all possible JSON-RPC 2.0 responses\nfor the A2A specification methods."}, "JSONRPCSuccessResponse": {"description": "Represents a successful JSON-RPC 2.0 Response object.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"description": "The value of this member is determined by the method invoked on the Server."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "ListTaskPushNotificationConfigParams": {"description": "Defines parameters for listing all push notification configurations associated with a task.", "properties": {"id": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with the request.", "type": "object"}}, "required": ["id"], "type": "object"}, "ListTaskPushNotificationConfigRequest": {"description": "Represents a JSON-RPC request for the `tasks/pushNotificationConfig/list` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/pushNotificationConfig/list", "description": "The method name. Must be 'tasks/pushNotificationConfig/list'.", "type": "string"}, "params": {"$ref": "#/definitions/ListTaskPushNotificationConfigParams", "description": "The parameters identifying the task whose configurations are to be listed."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "ListTaskPushNotificationConfigResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/ListTaskPushNotificationConfigSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/pushNotificationConfig/list` method."}, "ListTaskPushNotificationConfigSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/list` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"description": "The result, containing an array of all push notification configurations for the task.", "items": {"$ref": "#/definitions/TaskPushNotificationConfig"}, "type": "array"}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "Message": {"description": "Represents a single message in the conversation between a user and an agent.", "properties": {"contextId": {"description": "The context ID for this message, used to group related interactions.", "type": "string"}, "extensions": {"description": "The URIs of extensions that are relevant to this message.", "items": {"type": "string"}, "type": "array"}, "kind": {"const": "message", "description": "The type of this object, used as a discriminator. Always 'message' for a Message.", "type": "string"}, "messageId": {"description": "A unique identifier for the message, typically a UUID, generated by the sender.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions. The key is an extension-specific identifier.", "type": "object"}, "parts": {"description": "An array of content parts that form the message body. A message can be\ncomposed of multiple parts of different types (e.g., text and files).", "items": {"$ref": "#/definitions/Part"}, "type": "array"}, "referenceTaskIds": {"description": "A list of other task IDs that this message references for additional context.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Identifies the sender of the message. `user` for the client, `agent` for the service.", "enum": ["agent", "user"], "type": "string"}, "taskId": {"description": "The ID of the task this message is part of. Can be omitted for the first message of a new task.", "type": "string"}}, "required": ["kind", "messageId", "parts", "role"], "type": "object"}, "MessageSendConfiguration": {"description": "Defines configuration options for a `message/send` or `message/stream` request.", "properties": {"acceptedOutputModes": {"description": "A list of output MIME types the client is prepared to accept in the response.", "items": {"type": "string"}, "type": "array"}, "blocking": {"description": "If true, the client will wait for the task to complete. The server may reject this if the task is long-running.", "type": "boolean"}, "historyLength": {"description": "The number of most recent messages from the task's history to retrieve in the response.", "type": "integer"}, "pushNotificationConfig": {"$ref": "#/definitions/PushNotificationConfig", "description": "Configuration for the agent to send push notifications for updates after the initial response."}}, "type": "object"}, "MessageSendParams": {"description": "Defines the parameters for a request to send a message to an agent. This can be used\nto create a new task, continue an existing one, or restart a task.", "properties": {"configuration": {"$ref": "#/definitions/MessageSendConfiguration", "description": "Optional configuration for the send request."}, "message": {"$ref": "#/definitions/Message", "description": "The message object being sent to the agent."}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions.", "type": "object"}}, "required": ["message"], "type": "object"}, "MethodNotFoundError": {"description": "An error indicating that the requested method does not exist or is not available.", "properties": {"code": {"const": -32601, "description": "The error code for a method not found error.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Method not found", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "MutualTLSSecurityScheme": {"description": "Defines a security scheme using mTLS authentication.", "properties": {"description": {"description": "An optional description for the security scheme.", "type": "string"}, "type": {"const": "mutualTLS", "description": "The type of the security scheme. Must be 'mutualTLS'.", "type": "string"}}, "required": ["type"], "type": "object"}, "OAuth2SecurityScheme": {"description": "Defines a security scheme using OAuth 2.0.", "properties": {"description": {"description": "An optional description for the security scheme.", "type": "string"}, "flows": {"$ref": "#/definitions/OAuthFlows", "description": "An object containing configuration information for the supported OAuth 2.0 flows."}, "oauth2MetadataUrl": {"description": "URL to the oauth2 authorization server metadata\n[RFC8414](https://datatracker.ietf.org/doc/html/rfc8414). TLS is required.", "type": "string"}, "type": {"const": "oauth2", "description": "The type of the security scheme. Must be 'oauth2'.", "type": "string"}}, "required": ["flows", "type"], "type": "object"}, "OAuthFlows": {"description": "Defines the configuration for the supported OAuth 2.0 flows.", "properties": {"authorizationCode": {"$ref": "#/definitions/AuthorizationCodeOAuthFlow", "description": "Configuration for the OAuth Authorization Code flow. Previously called accessCode in OpenAPI 2.0."}, "clientCredentials": {"$ref": "#/definitions/ClientCredentialsOAuthFlow", "description": "Configuration for the OAuth Client Credentials flow. Previously called application in OpenAPI 2.0."}, "implicit": {"$ref": "#/definitions/ImplicitOAuthFlow", "description": "Configuration for the OAuth Implicit flow."}, "password": {"$ref": "#/definitions/PasswordOAuthFlow", "description": "Configuration for the OAuth Resource Owner Password flow."}}, "type": "object"}, "OpenIdConnectSecurityScheme": {"description": "Defines a security scheme using OpenID Connect.", "properties": {"description": {"description": "An optional description for the security scheme.", "type": "string"}, "openIdConnectUrl": {"description": "The OpenID Connect Discovery URL for the OIDC provider's metadata.", "type": "string"}, "type": {"const": "openIdConnect", "description": "The type of the security scheme. Must be 'openIdConnect'.", "type": "string"}}, "required": ["openIdConnectUrl", "type"], "type": "object"}, "Part": {"anyOf": [{"$ref": "#/definitions/TextPart"}, {"$ref": "#/definitions/FilePart"}, {"$ref": "#/definitions/DataPart"}], "description": "A discriminated union representing a part of a message or artifact, which can\nbe text, a file, or structured data."}, "PartBase": {"description": "Defines base properties common to all message or artifact parts.", "properties": {"metadata": {"additionalProperties": {}, "description": "Optional metadata associated with this part.", "type": "object"}}, "type": "object"}, "PasswordOAuthFlow": {"description": "Defines configuration details for the OAuth 2.0 Resource Owner Password flow.", "properties": {"refreshUrl": {"description": "The URL to be used for obtaining refresh tokens. This MUST be a URL.", "type": "string"}, "scopes": {"additionalProperties": {"type": "string"}, "description": "The available scopes for the OAuth2 security scheme. A map between the scope\nname and a short description for it.", "type": "object"}, "tokenUrl": {"description": "The token URL to be used for this flow. This MUST be a URL.", "type": "string"}}, "required": ["scopes", "tokenUrl"], "type": "object"}, "PushNotificationAuthenticationInfo": {"description": "Defines authentication details for a push notification endpoint.", "properties": {"credentials": {"description": "Optional credentials required by the push notification endpoint.", "type": "string"}, "schemes": {"description": "A list of supported authentication schemes (e.g., 'Basic', 'Bearer').", "items": {"type": "string"}, "type": "array"}}, "required": ["schemes"], "type": "object"}, "PushNotificationConfig": {"description": "Defines the configuration for setting up push notifications for task updates.", "properties": {"authentication": {"$ref": "#/definitions/PushNotificationAuthenticationInfo", "description": "Optional authentication details for the agent to use when calling the notification URL."}, "id": {"description": "A unique identifier (e.g. UUID) for the push notification configuration, set by the client\nto support multiple notification callbacks.", "type": "string"}, "token": {"description": "A unique token for this task or session to validate incoming push notifications.", "type": "string"}, "url": {"description": "The callback URL where the agent should send push notifications.", "type": "string"}}, "required": ["url"], "type": "object"}, "PushNotificationNotSupportedError": {"description": "An A2A-specific error indicating that the agent does not support push notifications.", "properties": {"code": {"const": -32003, "description": "The error code for when push notifications are not supported.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Push Notification is not supported", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "SecurityScheme": {"anyOf": [{"$ref": "#/definitions/APIKeySecurityScheme"}, {"$ref": "#/definitions/HTTPAuthSecurityScheme"}, {"$ref": "#/definitions/OAuth2SecurityScheme"}, {"$ref": "#/definitions/OpenIdConnectSecurityScheme"}, {"$ref": "#/definitions/MutualTLSSecurityScheme"}], "description": "Defines a security scheme that can be used to secure an agent's endpoints.\nThis is a discriminated union type based on the OpenAPI 3.0 Security Scheme Object."}, "SecuritySchemeBase": {"description": "Defines base properties shared by all security scheme objects.", "properties": {"description": {"description": "An optional description for the security scheme.", "type": "string"}}, "type": "object"}, "SendMessageRequest": {"description": "Represents a JSON-RPC request for the `message/send` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "message/send", "description": "The method name. Must be 'message/send'.", "type": "string"}, "params": {"$ref": "#/definitions/MessageSendParams", "description": "The parameters for sending a message."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "SendMessageResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/SendMessageSuccessResponse"}], "description": "Represents a JSON-RPC response for the `message/send` method."}, "SendMessageSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `message/send` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"anyOf": [{"$ref": "#/definitions/Task"}, {"$ref": "#/definitions/Message"}], "description": "The result, which can be a direct reply Message or the initial Task object."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "SendStreamingMessageRequest": {"description": "Represents a JSON-RPC request for the `message/stream` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "message/stream", "description": "The method name. Must be 'message/stream'.", "type": "string"}, "params": {"$ref": "#/definitions/MessageSendParams", "description": "The parameters for sending a message."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "SendStreamingMessageResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/SendStreamingMessageSuccessResponse"}], "description": "Represents a JSON-RPC response for the `message/stream` method."}, "SendStreamingMessageSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `message/stream` method.\nThe server may send multiple response objects for a single request.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"anyOf": [{"$ref": "#/definitions/Task"}, {"$ref": "#/definitions/Message"}, {"$ref": "#/definitions/TaskStatusUpdateEvent"}, {"$ref": "#/definitions/TaskArtifactUpdateEvent"}], "description": "The result, which can be a Message, Task, or a streaming update event."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "SetTaskPushNotificationConfigRequest": {"description": "Represents a JSON-RPC request for the `tasks/pushNotificationConfig/set` method.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/pushNotificationConfig/set", "description": "The method name. Must be 'tasks/pushNotificationConfig/set'.", "type": "string"}, "params": {"$ref": "#/definitions/TaskPushNotificationConfig", "description": "The parameters for setting the push notification configuration."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "SetTaskPushNotificationConfigResponse": {"anyOf": [{"$ref": "#/definitions/JSONRPCErrorResponse"}, {"$ref": "#/definitions/SetTaskPushNotificationConfigSuccessResponse"}], "description": "Represents a JSON-RPC response for the `tasks/pushNotificationConfig/set` method."}, "SetTaskPushNotificationConfigSuccessResponse": {"description": "Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/set` method.", "properties": {"id": {"description": "The identifier established by the client.", "type": ["string", "integer", "null"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "result": {"$ref": "#/definitions/TaskPushNotificationConfig", "description": "The result, containing the configured push notification settings."}}, "required": ["id", "jsonrpc", "result"], "type": "object"}, "Task": {"description": "Represents a single, stateful operation or conversation between a client and an agent.", "properties": {"artifacts": {"description": "A collection of artifacts generated by the agent during the execution of the task.", "items": {"$ref": "#/definitions/Artifact"}, "type": "array"}, "contextId": {"description": "A server-generated unique identifier (e.g. UUID) for maintaining context across multiple related tasks or interactions.", "type": "string"}, "history": {"description": "An array of messages exchanged during the task, representing the conversation history.", "items": {"$ref": "#/definitions/Message"}, "type": "array"}, "id": {"description": "A unique identifier (e.g. UUID) for the task, generated by the server for a new task.", "type": "string"}, "kind": {"const": "task", "description": "The type of this object, used as a discriminator. Always 'task' for a Task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions. The key is an extension-specific identifier.", "type": "object"}, "status": {"$ref": "#/definitions/TaskStatus", "description": "The current status of the task, including its state and a descriptive message."}}, "required": ["contextId", "id", "kind", "status"], "type": "object"}, "TaskArtifactUpdateEvent": {"description": "An event sent by the agent to notify the client that an artifact has been\ngenerated or updated. This is typically used in streaming models.", "properties": {"append": {"description": "If true, the content of this artifact should be appended to a previously sent artifact with the same ID.", "type": "boolean"}, "artifact": {"$ref": "#/definitions/Artifact", "description": "The artifact that was generated or updated."}, "contextId": {"description": "The context ID associated with the task.", "type": "string"}, "kind": {"const": "artifact-update", "description": "The type of this event, used as a discriminator. Always 'artifact-update'.", "type": "string"}, "lastChunk": {"description": "If true, this is the final chunk of the artifact.", "type": "boolean"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions.", "type": "object"}, "taskId": {"description": "The ID of the task this artifact belongs to.", "type": "string"}}, "required": ["artifact", "contextId", "kind", "taskId"], "type": "object"}, "TaskIdParams": {"description": "Defines parameters containing a task ID, used for simple task operations.", "properties": {"id": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with the request.", "type": "object"}}, "required": ["id"], "type": "object"}, "TaskNotCancelableError": {"description": "An A2A-specific error indicating that the task is in a state where it cannot be canceled.", "properties": {"code": {"const": -32002, "description": "The error code for a task that cannot be canceled.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Task cannot be canceled", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "TaskNotFoundError": {"description": "An A2A-specific error indicating that the requested task ID was not found.", "properties": {"code": {"const": -32001, "description": "The error code for a task not found error.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "Task not found", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}, "TaskPushNotificationConfig": {"description": "A container associating a push notification configuration with a specific task.", "properties": {"pushNotificationConfig": {"$ref": "#/definitions/PushNotificationConfig", "description": "The push notification configuration for this task."}, "taskId": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}}, "required": ["pushNotificationConfig", "taskId"], "type": "object"}, "TaskQueryParams": {"description": "Defines parameters for querying a task, with an option to limit history length.", "properties": {"historyLength": {"description": "The number of most recent messages from the task's history to retrieve.", "type": "integer"}, "id": {"description": "The unique identifier (e.g. UUID) of the task.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with the request.", "type": "object"}}, "required": ["id"], "type": "object"}, "TaskResubscriptionRequest": {"description": "Represents a JSON-RPC request for the `tasks/resubscribe` method, used to resume a streaming connection.", "properties": {"id": {"description": "The identifier for this request.", "type": ["string", "integer"]}, "jsonrpc": {"const": "2.0", "description": "The version of the JSON-RPC protocol. MUST be exactly \"2.0\".", "type": "string"}, "method": {"const": "tasks/resubscribe", "description": "The method name. Must be 'tasks/resubscribe'.", "type": "string"}, "params": {"$ref": "#/definitions/TaskIdParams", "description": "The parameters identifying the task to resubscribe to."}}, "required": ["id", "jsonrpc", "method", "params"], "type": "object"}, "TaskState": {"description": "Defines the lifecycle states of a Task.", "enum": ["submitted", "working", "input-required", "completed", "canceled", "failed", "rejected", "auth-required", "unknown"], "type": "string"}, "TaskStatus": {"description": "Represents the status of a task at a specific point in time.", "properties": {"message": {"$ref": "#/definitions/Message", "description": "An optional, human-readable message providing more details about the current status."}, "state": {"$ref": "#/definitions/TaskState", "description": "The current state of the task's lifecycle."}, "timestamp": {"description": "An ISO 8601 datetime string indicating when this status was recorded.", "examples": ["2023-10-27T10:00:00Z"], "type": "string"}}, "required": ["state"], "type": "object"}, "TaskStatusUpdateEvent": {"description": "An event sent by the agent to notify the client of a change in a task's status.\nThis is typically used in streaming or subscription models.", "properties": {"contextId": {"description": "The context ID associated with the task.", "type": "string"}, "final": {"description": "If true, this is the final event in the stream for this interaction.", "type": "boolean"}, "kind": {"const": "status-update", "description": "The type of this event, used as a discriminator. Always 'status-update'.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata for extensions.", "type": "object"}, "status": {"$ref": "#/definitions/TaskStatus", "description": "The new status of the task."}, "taskId": {"description": "The ID of the task that was updated.", "type": "string"}}, "required": ["contextId", "final", "kind", "status", "taskId"], "type": "object"}, "TextPart": {"description": "Represents a text segment within a message or artifact.", "properties": {"kind": {"const": "text", "description": "The type of this part, used as a discriminator. Always 'text'.", "type": "string"}, "metadata": {"additionalProperties": {}, "description": "Optional metadata associated with this part.", "type": "object"}, "text": {"description": "The string content of the text part.", "type": "string"}}, "required": ["kind", "text"], "type": "object"}, "TransportProtocol": {"description": "Supported A2A transport protocols.", "enum": ["JSONRPC", "GRPC", "HTTP+JSON"], "type": "string"}, "UnsupportedOperationError": {"description": "An A2A-specific error indicating that the requested operation is not supported by the agent.", "properties": {"code": {"const": -32004, "description": "The error code for an unsupported operation.", "type": "integer"}, "data": {"description": "A primitive or structured value containing additional information about the error.\nThis may be omitted."}, "message": {"default": "This operation is not supported", "description": "The error message.", "type": "string"}}, "required": ["code", "message"], "type": "object"}}}