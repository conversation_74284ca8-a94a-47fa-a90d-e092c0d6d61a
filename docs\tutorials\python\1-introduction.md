# Python Quickstart Tutorial: Building an A2A Agent

Welcome to the Agent2Agent (A2A) Python Quickstart Tutorial!

In this tutorial, you will explore a simple "echo" A2A server using the Python SDK. This will introduce you to the fundamental concepts and components of an A2A server. You will then look at a more advanced example that integrates a Large Language Model (LLM).

This hands-on guide will help you understand:

- The basic concepts behind the A2A protocol.
- How to set up a Python environment for A2A development using the SDK.
- How Agent Skills and Agent Cards describe an agent.
- How an A2A server handles tasks.
- How to interact with an A2A server using a client.
- How streaming capabilities and multi-turn interactions work.
- How an LLM can be integrated into an A2A agent.

By the end of this tutorial, you will have a functional understanding of A2A agents and a solid foundation for building or integrating A2A-compliant applications.

## Tutorial Sections

The tutorial is broken down into the following steps:

1. **[Introduction (This Page)](./1-introduction.md)**
2. **[Setup](./2-setup.md)**: Prepare your Python environment and the A2A SDK.
3. **[Agent Skills & Agent Card](./3-agent-skills-and-card.md)**: Define what your agent can do and how it describes itself.
4. **[The Agent Executor](./4-agent-executor.md)**: Understand how the agent logic is implemented.
5. **[Starting the Server](./5-start-server.md)**: Run the Helloworld A2A server.
6. **[Interacting with the Server](./6-interact-with-server.md)**: Send requests to your agent.
7. **[Streaming & Multi-Turn Interactions](./7-streaming-and-multiturn.md)**: Explore advanced capabilities with the LangGraph example.
8. **[Next Steps](./8-next-steps.md)**: Explore further possibilities with A2A.

Let's get started!
