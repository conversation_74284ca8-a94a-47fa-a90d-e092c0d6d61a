# Project information
site_name: Agent2Agent (A2A) Protocol
site_url: https://a2a-protocol.org/
site_description: >-
  An open protocol enabling communication and interoperability between opaque agentic applications.
site_dir: site

extra:
  a2a_version: !ENV [MIKE_VERSION, 'dev']
  analytics:
    provider: google
    property: G-RB39M83WHB


# Navigation
nav:
  - Home: index.md
  - Topics:
    - What is A2A?: topics/what-is-a2a.md
    - Key Concepts: topics/key-concepts.md
    - A2A and MCP: topics/a2a-and-mcp.md
    - Agent Discovery: topics/agent-discovery.md
    - Enterprise-Ready Features: topics/enterprise-ready.md
    - Streaming & Asynchronous Operations: topics/streaming-and-async.md
    - Extensions: topics/extensions.md
    - Life of a Task: topics/life-of-a-task.md
  - Specification: specification.md
  - Community: community.md
  - Partners: partners.md
  - SDK Reference:
    - Python: sdk/python/api/index.html
  - Tutorial (Python):
    - Introduction: tutorials/python/1-introduction.md
    - Setup: tutorials/python/2-setup.md
    - Agent Skills & Agent Card: tutorials/python/3-agent-skills-and-card.md
    - Agent Executor: tutorials/python/4-agent-executor.md
    - Start Server: tutorials/python/5-start-server.md
    - Interact with Server: tutorials/python/6-interact-with-server.md
    - Streaming & Multiturn: tutorials/python/7-streaming-and-multiturn.md
    - Next Steps: tutorials/python/8-next-steps.md
  - Roadmap: roadmap.md

# Repository
repo_name: a2aproject/A2A
repo_url: https://github.com/a2aproject/A2A

# Copyright
copyright: Copyright Google 2025

# Custom CSS
extra_css:
  - stylesheets/custom.css

# Configuration
theme:
  name: material
  custom_dir: .mkdocs/overrides
  font:
    text: Google Sans
    code: Roboto Mono
  logo: assets/a2a-logo-white.svg
  favicon: assets/a2a-logo-black.svg
  icon:
    repo: fontawesome/brands/github
    admonition:
      note: fontawesome/solid/note-sticky
      abstract: fontawesome/solid/book
      info: fontawesome/solid/circle-info
      tip: fontawesome/solid/bullhorn
      success: fontawesome/solid/check
      question: fontawesome/solid/circle-question
      warning: fontawesome/solid/triangle-exclamation
      failure: fontawesome/solid/bomb
      danger: fontawesome/solid/skull
      bug: fontawesome/solid/robot
      example: fontawesome/solid/flask
      quote: fontawesome/solid/quote-left
  palette:
    - scheme: default
      primary: indigo
      accent: white
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: indigo
      accent: white
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - announce.dismiss
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - content.tabs.link
    - navigation.expand
    - navigation.footer
    - navigation.indexes
    - navigation.instant
    - navigation.instant.progress
    - navigation.path
    - navigation.tabs
    - navigation.top
    - navigation.tracking
    - toc.follow
    - versioning:
        provider: mike

# Extensions
markdown_extensions:
  - footnotes
  - admonition
  - attr_list
  - md_in_html
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets:
      url_download: true
      dedent_subsections: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - toc:
      permalink: true

# Plugins
plugins:
  - search
  - macros
  - redirects:
      redirect_maps:
        "spec-json.md": https://raw.githubusercontent.com/a2aproject/A2A/refs/heads/main/specification/json/a2a.json
        "specification/overview.md": "specification.md"
        "specification/agent-card.md": "specification.md#5-agent-discovery-the-agent-card"
        "specification/agent-to-agent-communication.md": "specification.md#6-protocol-data-objects"
        "specification/sample-messages.md": "specification.md#9-common-workflows-examples"
        "specification/details.md": "specification.md"
        "documentation.md": "topics/key-concepts.md"
        "resources.md": "index.md"
        "topics/push-notifications.md": "topics/streaming-and-async.md"
        "specification/topics/a2a_and_mcp.md": "topics/a2a-and-mcp.md"
        "specification/topics/agent_discovery.md": "topics/agent-discovery.md"
        "specification/topics/enterprise_ready.md": "topics/enterprise-ready.md"
        "specification/topics/push_notification.md": "topics/streaming-and-async.md"
        "specification/topics/push_notifications.md": "topics/streaming-and-async.md"
        "topics/index.md": "topics/what-is-a2a.md"
        "topics/roadmap.md": "roadmap.md"
        # Tutorial
        "tutorials/index.md": "tutorials/python/1-introduction.md"
        "tutorials/python/index.md": "tutorials/python/1-introduction.md"
        "tutorials/python/1_introduction.md": "tutorials/python/1-introduction.md"
        "tutorials/python/3-create-project.md": "tutorials/python/2-setup.md"
        "tutorials/python/4-agent-skills.md": "tutorials/python/3-agent-skills-and-card.md"
        "tutorials/python/5-add-agent-card.md": "tutorials/python/3-agent-skills-and-card.md"
        "tutorials/python/6-start-server.md": "tutorials/python/5-start-server.md"
        "tutorials/python/7-interact-with-server.md": "tutorials/python/6-interact-with-server.md"
        "tutorials/python/8-agent-capabilities.md": "tutorials/python/7-streaming-and-multiturn.md"
        "tutorials/python/9-ollama-agent.md": "tutorials/python/7-streaming-and-multiturn.md"
        "tutorials/python/10-next-steps.md": "tutorials/python/8-next-steps.md"
        "sdk/python/": "sdk/python/api/index.html"
