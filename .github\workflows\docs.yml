name: Docs Build and Deploy

on:
  push:
    branches:
      - main
    paths:
      - ".github/workflows/docs.yml"
      - ".github/scripts/deploy-404.sh"
      - "requirements-docs.txt"
      - "mkdocs.yml"
      - "docs/**"
  pull_request:
    branches:
      - main
    paths:
      - ".github/workflows/docs.yml"
      - ".github/scripts/deploy-404.sh"
      - "requirements-docs.txt"
      - "mkdocs.yml"
      - "docs/**"
  release:
    types:
      - published

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      actions: read

    if: github.repository == 'a2aproject/A2A'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Configure Git Credentials
        run: |
          git config --global user.name github-actions[bot]
          git config --global user.email 41898282+github-actions[bot]@users.noreply.github.com

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.13

      - name: Restore pip cache
        uses: actions/cache@v4
        with:
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements-docs.txt') }}
          path: ~/.cache/pip
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install documentation dependencies
        run: pip install -r requirements-docs.txt

      - name: Generate and Build Python SDK Docs
        run: |
          # Find the path to the installed package
          PACKAGE_PATH=$(python -c "import a2a, os; print(os.path.dirname(a2a.__file__))")
          echo "Found 'a2a' package at: ${PACKAGE_PATH}"

          # Generate RST source files from the installed package
          sphinx-apidoc -f -o docs/sdk/python "${PACKAGE_PATH}"

          # Build the HTML from the generated RST files
          sphinx-build -b html docs/sdk/python docs/sdk/python/api

      - name: Build Documentation (PR Check)
        if: github.event_name == 'pull_request'
        run: mkdocs build

      - name: Deploy development version from main branch
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: |
          mike deploy --push --update-aliases dev latest

          echo "Setting 'latest' as the default version for the site..."
          mike set-default --push latest

          # Deploy 404 page
          bash .github/scripts/deploy-404.sh ${{ github.repository }} ${{ secrets.GITHUB_TOKEN }}

      - name: Deploy new release version and set as latest
        if: github.event_name == 'release'
        run: |
          # The release tag (e.g., v0.2.2) is used as the version number
          export MIKE_VERSION=${{ github.event.release.tag_name }}
          echo "Deploying docs for version $MIKE_VERSION and setting it as 'latest'..."
          mike deploy --push --update-aliases $MIKE_VERSION latest

          echo "Setting 'latest' as the default version for the site..."
          mike set-default --push latest

          # Call the reusable script to deploy the 404 page, passing the token
          bash .github/scripts/deploy-404.sh ${{ github.repository }} ${{ secrets.GITHUB_TOKEN }}
