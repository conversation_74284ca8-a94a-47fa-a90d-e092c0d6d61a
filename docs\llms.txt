# A2A (Agent2Agent) Protocol High-Level Summary

This project defines and demonstrates the **Agent2Agent (A2A) protocol**, an open standard initiated by Google designed to enable communication and interoperability between disparate AI agent systems. The core goal is to allow agents built on different frameworks (e.g., LangGraph, CrewAI, Google ADK, Genkit) or by different vendors to discover each other's capabilities, negotiate interaction modes (text, forms, files, potentially audio/video later), and collaborate on tasks.

The repository provides:

1. **Formal Specification:** A detailed JSON Schema (`specification/json/a2a.json`) defining the structure of A2A messages, including requests, responses, task states, artifacts, and agent metadata (Agent Cards).
2. **Core Concepts Documentation (Implied):** Links in the main README suggest documentation covering agent discovery, task lifecycle, artifact exchange, streaming updates, push notifications, and enterprise readiness.
3. **Sample Implementations:**
    - **Common Libraries:** Reusable Python (`samples/python/common`) and JavaScript/TypeScript (`samples/js/src`) code for building A2A clients and servers, handling JSON-RPC communication, task management, and potentially authentication.
    - **Example Agents:** Demonstrations of integrating A2A into various agent frameworks:
        - **Python:** LangGraph (currency conversion, streaming), CrewAI (image generation, file artifacts), Google ADK (expense reports, form handling).
        - **JavaScript/TypeScript:** Genkit (movie info via API, code generation with file artifacts).
    - **Example Hosts:** Applications that *consume* A2A services:
        - CLIs in both Python and JS for direct interaction.
        - A Python-based multi-agent orchestrator (using Google ADK) that delegates tasks to other A2A agents.
4. **Demo Web Application:** A web UI (`demo/ui`, likely using Mesop) demonstrating multi-agent interactions facilitated by the A2A protocol, including visualization of conversations, tasks, events, and agent discovery. It features a backend service coordinating with the host agent.

Key features of the A2A protocol highlighted by the specification and samples include: agent discovery via Agent Cards, standardized task management (send, get, cancel), support for different content types (text, files, structured data) via `Parts` and `Artifacts`, streaming updates for long-running tasks, and mechanisms for push notifications. The project is open source and encourages community contribution.

# A2A (Agent2Agent) Protocol

## 1. Overview

- **Project Name:** Agent2Agent (A2A) Protocol
- **Purpose:** An open protocol by Google enabling communication and interoperability between AI agents built on different frameworks or by different vendors.
- **Core Goal:** Allow agents to discover capabilities, negotiate interaction, and collaborate securely on tasks.
- **Communication:** Uses JSON-RPC 2.0 over HTTP(S). Supports standard request/response and Server-Sent Events (SSE) for streaming.
- **Key Components:** Specification (JSON Schema), Common Libraries (Python, JS/TS), Sample Agents (LangGraph, CrewAI, ADK, Genkit), Sample Hosts (CLI, Orchestrator), Demo Web App (Mesop).

## 2. Protocol Specification (`specification/json/a2a.json`)

### 2.1. Core JSON-RPC Structures

- **`JSONRPCMessage`:** Base for requests/responses. Contains `jsonrpc: "2.0"` and optional `id`.
- **`JSONRPCRequest`:** Represents a request.
    - `method`: String identifying the operation (e.g., "tasks/send").
    - `params`: Object or Array containing parameters for the method.
    - `id`: Unique identifier (string/number) for request/response correlation. Omitted/null for notifications.
- **`JSONRPCResponse`:** Represents a response.
    - `result`: Contains the successful result data (can be `null`). Mutually exclusive with `error`.
    - `error`: Contains an error object if the request failed. Mutually exclusive with `result`.
    - `id`: Must match the corresponding request `id`.
- **`JSONRPCError`:** Represents an error.
    - `code`: Integer error code.
    - `message`: String description of the error.
    - `data`: Optional additional error details.

### 2.2. Key A2A Data Objects

- **`AgentCard`:** Metadata describing an agent. Found typically at `/.well-known/agent-card.json`.
    - `name`: (string) Human-readable name.
    - `description`: (string) Agent description.
    - `url`: (string) Base URL endpoint for the agent's A2A service.
    - `provider`: (`AgentProvider`) Organization details (optional).
    - `version`: (string) Agent/API version.
    - `documentationUrl`: (string) Link to documentation (optional).
    - `capabilities`: (`AgentCapabilities`) Features supported (streaming, push).
    - `securitySchemes`: (Object) Security scheme details for authentication (optional).
    - `security`: (Array) Security requirements for contacting the agent (optional).
    - `defaultInputModes`: (string[]) Default supported input types (e.g., "text/plain", "application/json").
    - `defaultOutputModes`: (string[]) Default supported output types.
    - `skills`: (`AgentSkill[]`) List of specific capabilities.
    - `supportsAuthenticatedExtendedCard`: (boolean) Indicates support for retrieving a more detailed Agent Card via an authenticated endpoint (optional).
- **`AgentCapabilities`:**
    - `streaming`: (boolean) Supports `message/stream` and `tasks/resubscribe` for real-time updates via Server-Sent Events (SSE). Default: `false`.
    - `pushNotifications`: (boolean) Supports `tasks/pushNotificationConfig/set|get` for asynchronous task updates via webhooks. Default: `false`.
    - `stateTransitionHistory`: (boolean) Supports providing detailed history of status changes within the Task object (future enhancement). Default: `false`.
- **`AgentSkill`:**
    - `id`: (string) Unique skill ID within this agent.
    - `name`: (string) Human-readable skill name.
    - `description`: (string) Detailed skill description. CommonMark may be used.
    - `tags`: (string[]) Keywords/categories for discoverability.
    - `examples`: (string[]) Example prompts or use cases demonstrating skill usage (optional).
    - `inputModes`: (string[]) Overrides `defaultInputModes` for this specific skill. Accepted Media Types (optional).
    - `outputModes`: (string[]) Overrides `defaultOutputModes` for this specific skill. Produced Media Types (optional).
- **`Task`:** Represents a unit of work processed by an agent.
    - `id`: (string) Unique task identifier.
    - `contextId`: (string) Groups related tasks in a conversation or session.
    - `status`: (`TaskStatus`) Current state information.
    - `artifacts`: (`Artifact[]`) Files/data produced by the agent (optional).
    - `history`: (`TaskStatus[]`) Status transition history (optional, requires `stateTransitionHistory` capability).
    - `metadata`: (object) Custom key-value data for client use (optional).
    - `kind`: ("task") Type discriminator.
- **`TaskStatus`:**
    - `state`: (`TaskState`) Current lifecycle state (enum).
    - `message`: (string) Human-readable status message (optional).
    - `timestamp`: (string) ISO-8601 timestamp of when this status was set.
    - `kind`: ("status") Type discriminator.
- **`TaskState`:** (enum)
    - `submitted`: Task received but not yet processing (non-terminal).
    - `working`: Task is actively being processed (non-terminal).
    - `input_required`: Task requires additional input from the client to proceed (non-terminal).
    - `completed`: Task completed successfully (terminal).
    - `failed`: Task encountered an error (terminal).
    - `canceled`: Task was canceled by client request (terminal).
    - `rejected`: Task was rejected by the agent (terminal).
    - `auth-required`: Authentication required from client/user to proceed (non-terminal).
    - `unknown`: Task state cannot be determined (terminal).
- **`Message`:** Communication unit between user and agent.
    - `role`: ("user" | "agent") Sender role.
    - `parts`: (`Part[]`) Content parts (text, file, data).
    - `metadata`: (object | null) Message-specific metadata.
    - `messageId`: (string) Unique identifier for the message.
    - `parentMessageId`: (string) Reference to a previous message being replied to (optional).
    - `rootMessageId`: (string) Reference to the first message in a thread (optional).
    - `referenceTaskIds`: (string[]) List of tasks referenced as contextual hint by this message (optional).
    - `taskId`: (string) Task identifier the current message is related to (optional).
    - `contextId`: (string) Context identifier the message is associated with (optional).
    - `kind`: ("message") Type discriminator.
- **`Part` (Union Type):** Represents a piece of content within a Message or Artifact.
    - **`TextPart`:**
        - `type`: "text"
        - `text`: (string) Text content.
        - `mimeType`: (string) Format specification (default: "text/plain").
    - **`FilePart`:**
        - `type`: "file"
        - `file`: (`FileWithUri`) File reference with URI.
        - `inline`: (boolean) Whether the file content should be displayed inline (optional).
    - **`DataPart`:**
        - `type`: "data"
        - `data`: (any) Structured data in JSON-serializable format.
        - `mimeType`: (string) Format specification (e.g., "application/json").
        - `inline`: (boolean) Whether the data should be displayed inline (optional).
    - `metadata`: (object | null) Optional metadata for the specific part.
- **`FileWithUri`:** Represents a file reference with URI.
    - `name`: (string) Filename with extension.
    - `mimeType`: (string) Content type (Media Type).
    - `uri`: (string) URI to access the file content.
- **`Artifact`:** An output generated by a task.
    - `artifactId`: (string) Unique identifier for the artifact.
    - `name`: (string) Human-readable artifact name (optional).
    - `description`: (string) Detailed description of the artifact (optional).
    - `parts`: (`Part[]`) Content segments in the artifact.
    - `metadata`: (object) Custom key-value data (optional).
    - `kind`: ("artifact") Type discriminator.
- **`AuthenticationInfo`:** Authentication details for push notifications.
    - `schemes`: (string[]) Array of authentication scheme names the caller must use.
    - `credentials`: (string) Optional static credentials or scheme-specific configuration info.
- **`PushNotificationConfig`:** Configuration for push notifications.
    - `url`: (string) Endpoint URL for the agent to POST notifications to.
    - `token`: (string) Optional authentication token for the agent to include in requests (optional).
    - `authentication`: (`AuthenticationInfo`) Authentication details the agent needs to call the URL (optional).
- **`TaskPushNotificationConfig`:** Associates a `PushNotificationConfig` with a task ID.
    - `taskId`: (string) The task ID to receive push notifications for.
    - `config`: (`PushNotificationConfig`) The push notification configuration to use.
    - `metadata`: (object) Custom key-value data for client use (optional).
- **`MessageSendParams`:** Parameters for sending a message.
    - `message`: (`Message`) The message to send.
    - `configuration`: (`MessageSendConfiguration`) Optional configuration for the message.
    - `metadata`: (object) Custom key-value data for client use (optional).
- **`agent/getAuthenticatedExtendedCard`:** (JSON-RPC Method)
    - Retrieves a potentially more detailed version of the Agent Card after the client has authenticated.
    - Available only if `AgentCard.supportsAuthenticatedExtendedCard` is `true`.
    - Authentication: Required using one of the schemes declared in the public `AgentCard.securitySchemes`.
    - Response: Complete `AgentCard` object with potentially additional details.

### 2.3. A2A RPC Methods

- **`message/send`:** (Request/Response)
    - Sends a message to initiate or continue a task.
    - `params`: `MessageSendParams` (includes `taskId`, `contextId`, `message`, optionally `configuration`).
    - `result`: `Task` (final state after synchronous processing).
- **`message/stream`:** (Request/Stream)
    - Sends a message and subscribes to real-time updates via Server-Sent Events (SSE).
    - `params`: `MessageSendParams`.
    - `result` (stream events): `SendStreamingMessageResponse` containing one of: `MessageEvent`, `TaskStatusUpdateEvent`, `TaskArtifactUpdateEvent`. Final event has `final: true`.
- **`tasks/get`:** (Request/Response)
    - Retrieves the current state of a task.
    - `params`: `TaskQueryParams` (includes `id`, optionally `historyLength`).
    - `result`: `Task`.
- **`tasks/cancel`:** (Request/Response)
    - Requests cancellation of a running task.
    - `params`: `TaskIdParams` (includes `id`).
    - `result`: `Task` (updated state, likely 'canceled') or error if not cancelable.
- **`tasks/pushNotificationConfig/set`:** (Request/Response)
    - Sets or updates the push notification configuration for a task.
    - `params`: `TaskPushNotificationConfig`.
    - `result`: `TaskPushNotificationConfig` (confirmed configuration).
- **`tasks/pushNotificationConfig/get`:** (Request/Response)
    - Retrieves the current push notification configuration for a task.
    - `params`: `TaskIdParams` (includes `taskId`).
    - `result`: `TaskPushNotificationConfig`.
- **`tasks/resubscribe`:** (Request/Stream)
    - Resubscribes to task updates after a connection interruption (SSE).
    - `params`: `TaskQueryParams`.
    - `result` (stream events): `TaskStatusUpdateEvent` or `TaskArtifactUpdateEvent`.

### 2.4. Streaming Update Events (Result of `message/stream` or `tasks/resubscribe`)

- **`SendStreamingMessageResponse`:** Contains a message from the agent.
    - `type`: "message"
    - `message`: (`Message`) The message content.
    - `contextId`: (string) Context identifier the message is associated with.
    - `kind`: ("streaming-response") Type discriminator.
    - `final`: (boolean) True if this is the final message for the task.
- **`TaskStatusUpdateEvent`:** Signals a change in task status.
    - `type`: "task-status"
    - `taskId`: (string) Task ID.
    - `contextId`: (string) Context identifier the task is associated with.
    - `kind`: ("status-update") Type discriminator.
    - `status`: (`TaskStatus`) The new status object.
    - `final`: (boolean) True if this is the terminal update for the task.
- **`TaskArtifactUpdateEvent`:** Signals a new or updated artifact.
    - `type`: "task-artifact"
    - `taskId`: (string) Task ID.
    - `artifact`: (`Artifact`) The artifact data.
    - `append`: (boolean) If true, append parts to artifact; if false (default), replace.
    - `lastChunk`: (boolean) If true, indicates this is the final update for the artifact.
    - `final`: (boolean) Usually false for artifacts, can signal end concurrently with status.

### 2.5. Standard Error Codes

- `-32700`: `JSONParseError` - Invalid JSON payload.
- `-32600`: `InvalidRequestError` - Invalid JSON-RPC request object.
- `-32601`: `MethodNotFoundError` - Method does not exist.
- `-32602`: `InvalidParamsError` - Invalid method parameters.
- `-32603`: `InternalError` - Internal server error.

### 2.6. A2A Specific Error Codes

- `-32001`: `TaskNotFoundError` - Specified task ID not found.
- `-32002`: `TaskNotCancelableError` - Task is in a terminal state and cannot be canceled.
- `-32003`: `PushNotificationNotSupportedError` - Agent does not support push notifications.
- `-32004`: `UnsupportedOperationError` - The requested operation is not supported.
- `-32005`: `ContentTypeNotSupportedError` - Mismatch in supported content types.
- `-32006`: `InvalidAgentResponseError` - Agent generated an invalid response for the requested method.

## 3. Core Concepts

- **Agent Discovery:** Clients find agents and their capabilities by fetching the `AgentCard` JSON, typically from `/.well-known/agent-card.json`.
- **Task Lifecycle:** Tasks progress through states defined in `TaskState` (submitted -> working -> [input_required] -> completed/failed/canceled/rejected/unknown).
- **Communication:** Uses `Message` objects containing `Part`s (text, file, data). Task outputs are represented as `Artifact`s, also containing `Part`s.
- **Streaming:** Long-running tasks can provide real-time updates using Server-Sent Events (SSE) via `message/stream`. Updates are sent as `MessageEvent`, `TaskStatusUpdateEvent` and `TaskArtifactUpdateEvent`. Reconnection after interruptions is supported via `tasks/resubscribe`.
- **Push Notifications:** Agents can proactively notify clients about task updates using webhook URLs provided via `tasks/pushNotificationConfig/set`. Authentication mechanisms (e.g., Bearer tokens via JWT signed with keys from agent's JWKS endpoint) are supported for secure communication.
- **Authentication:** Defined in `AgentCard` (via `securitySchemes` and `security` fields) and `PushNotificationConfig`. Can involve various schemes (e.g., API keys, OAuth, JWT). The protocol supports authenticated extended Agent Card retrieval via the `agent/getAuthenticatedExtendedCard` JSON-RPC method. Samples use JWT for push notifications and secure communication.
- **Forms:** Structured data can be requested and submitted using `DataPart` within Messages/Artifacts (demonstrated in ADK sample).

## 4. Security Considerations

- **Transport Security:** Always use HTTPS with strong TLS configurations in production environments.
- **Authentication:**
    - Handled via standard HTTP mechanisms (e.g., `Authorization` header with Bearer tokens, API keys).
    - Requirements are declared in the `AgentCard`.
    - Credentials MUST be obtained out-of-band by the client.
    - A2A Servers MUST authenticate every request.
- **Authorization:**
    - A server-side responsibility based on the authenticated identity.
    - Implement the principle of least privilege.
    - Can be granular, based on skills, actions, or data.
- **Push Notification Security:**
    - Webhook URL validation (by the A2A Server sending notifications) is crucial to prevent SSRF.
    - Authentication of the A2A Server to the client's webhook is essential.
    - Authentication of the notification by the client's webhook receiver (verifying it came from the legitimate A2A Server and is relevant) is critical.
- **Input Validation:** Servers MUST rigorously validate all RPC parameters and the content/structure of data in `Message` and `Artifact` parts to prevent injection attacks or processing errors.
- **Resource Management:** Implement rate limiting, concurrency controls, and resource limits to protect agents from abuse or overload.
- **Data Privacy:** Adhere to all applicable privacy regulations for data exchanged in `Message` and `Artifact` parts. Minimize sensitive data transfer.

## 4. Implementations & Samples

### 4.1. Common Libraries

- **Python (`samples/python/common`)**:
    - `client/`: `A2AClient` for making requests, `A2ACardResolver` for discovery.
    - `server/`: `A2AServer` (Starlette-based), `TaskManager` base class, `InMemoryTaskManager`.
    - `types.py`: Pydantic models mirroring the JSON schema.
    - `utils/`: Helpers for push notification auth (JWT signing/verification, JWKS endpoint).
- **JavaScript/TypeScript (`samples/js/src`)**:
    - `client/`: `A2AClient` implementation using `fetch`.
    - `server/`: `A2AServer` (Express-based), `TaskStore` interface, `InMemoryTaskStore`, `FileStore`.
    - `schema.ts`: TypeScript interfaces matching the JSON schema.
    - `handler.ts`, `error.ts`, `utils.ts`: Support code for the server.

### 4.2. Python Samples

- **Location:** `samples/python/agents/` & `samples/python/hosts/`
- **Setup:** Uses `uv` and `pyproject.toml`. Requires Python >= 3.12/3.13. API keys via `.env`.
- **Agents:**
    - **LangGraph (`agents/langgraph`)**: Currency conversion agent. Demonstrates tool use, multi-turn (`input-required`), and **streaming** (`tasks/sendSubscribe`).
    - **CrewAI (`agents/crewai`)**: Image generation agent. Demonstrates multi-turn and handling **file artifacts** (images).
    - **Google ADK (`agents/google_adk`)**: Expense reimbursement agent. Demonstrates multi-turn and handling **forms** using `DataPart`.
- **Hosts:**
    - **CLI (`hosts/cli`)**: Simple command-line client to interact with any A2A agent. Supports streaming and optional push notification listening.
    - **Multi-Agent Orchestrator (`hosts/multiagent`)**: An ADK-based "Host Agent" that manages connections (`RemoteAgentConnections`) to other A2A agents and delegates tasks based on instructions.

### 4.3. JavaScript/TypeScript Samples

- **Location:** `samples/js/`
- **Setup:** Uses `npm`/`pnpm`, `tsx`, `tsconfig.json`. Requires Node.js >= 18. API keys via environment variables. Framework: **Genkit**.
- **Agents (`src/agents/`)**:
    - **Movie Agent (`movie-agent`)**: Uses TMDB API via Genkit tools to answer movie questions. Demonstrates tool use and multi-turn (`AWAITING_USER_INPUT` mapped to `input-required`).
    - **Coder Agent (`coder`)**: Generates code files. Demonstrates producing multiple **file artifacts** via streaming updates. Uses custom Genkit format (`code-format.ts`).
- **Hosts:**
    - **CLI (`src/cli.ts`)**: Command-line client for interacting with JS agents.

### 4.4. Demo Application (`demo/`)

- **UI (`demo/ui`)**: Web application built with **Mesop**.
    - Visualizes conversations with multiple agents via the host orchestrator.
    - Renders text, images, forms.
    - Allows dynamic agent registration via URL.
    - Provides views for task list and event logs.
- **Service (`demo/ui/service`)**: Backend service for the Mesop UI.
    - `server/`: Manages conversations, routes messages, interfaces with the host agent (`ADKHostManager` or `InMemoryFakeAgentManager`).
    - `client/`: Client used by the UI to talk to its *own* backend service.

## 5. Development & Setup

- **Prerequisites:** Python (>=3.12 or 3.13), Node.js (>=18), `uv` (for Python), `npm`/`pnpm` (for JS).
- **API Keys:** Required for LLM access (e.g., `GOOGLE_API_KEY`, `TMDB_API_KEY`), typically set via `.env` files or environment variables.
- **Running Samples:** Generally involves running an agent server (`uv run ...` or `npm run ...`) and then a host client/app (e.g., `uv run hosts/cli --agent <agent_url>`).

## 6. Contribution

- See `CONTRIBUTING.md`.
- GitHub discussion available as the primary means of communication.
- GitHub issues for bugs and feature requests.
- Google Form for private feedback.
